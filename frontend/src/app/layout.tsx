import type { Metada<PERSON> } from "next";
import { Noto_Sans_JP } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/lib/auth";
import { ThemeProvider } from "@/lib/theme";
import Header from "@/components/Header";

const notoSansJP = Noto_Sans_JP({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "歌舞伎台本自動成形システム",
  description:
    "手書き赤入れPDFをOpenAI GPT-4oで自動読み取りし、縦書きWord文書へ整形するWebアプリケーション",
};

export default function RootLayout({ children }: { children: any }) {
  return (
    <html lang="ja">
      <body className={notoSansJP.className}>
        <ThemeProvider>
          <AuthProvider>
            <div className="min-h-screen bg-background text-foreground">
              <Header />
              <main className="px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8">
                {children}
              </main>
            </div>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
