"use client";

import { useCallback } from "react";
import { Noto_Serif_JP } from "next/font/google";
import { Paragraph, ParagraphType } from "@/types";

const notoSerifJP = Noto_Serif_JP({ subsets: ["latin"] });

interface ScriptEditorProps {
  paragraphs: Paragraph[];
  onChange: (paragraphs: Paragraph[]) => void;
}

export default function ScriptEditor({
  paragraphs,
  onChange,
}: ScriptEditorProps) {
  const handleUpdateParagraph = useCallback(
    (index: number, field: keyof Paragraph, value: any) => {
      const updatedParagraphs = paragraphs.map((paragraph, i) =>
        i === index ? { ...paragraph, [field]: value } : paragraph
      );
      onChange(updatedParagraphs);
    },
    [paragraphs, onChange]
  );

  const handleAddParagraph = useCallback(() => {
    const newParagraph: Paragraph = {
      type: "lines",
      header: "",
      content: "",
    };
    onChange([...paragraphs, newParagraph]);
  }, [paragraphs, onChange]);

  const handleDeleteParagraph = useCallback(
    (index: number) => {
      // 削除前の安全チェック
      if (index < 0 || index >= paragraphs.length) {
        console.warn("削除対象の段落が見つかりません");
        return;
      }

      const updatedParagraphs = paragraphs.filter((_, i) => i !== index);

      // 削除後の配列が空でないことを確認
      if (updatedParagraphs.length === 0) {
        console.log("すべての段落が削除されました");
      }

      onChange(updatedParagraphs);
    },
    [paragraphs, onChange]
  );

  // 段落を左に移動（インデックスを減らす）
  const handleMoveParagraphRight = useCallback(
    (index: number) => {
      if (index <= 0) return; // 最初の要素は左に移動できない

      const updatedParagraphs = [...paragraphs];
      const temp = updatedParagraphs[index];
      updatedParagraphs[index] = updatedParagraphs[index - 1];
      updatedParagraphs[index - 1] = temp;

      onChange(updatedParagraphs);
    },
    [paragraphs, onChange]
  );

  // 段落を右に移動（インデックスを増やす）
  const handleMoveParagraphLeft = useCallback(
    (index: number) => {
      if (index >= paragraphs.length - 1) return; // 最後の要素は右に移動できない

      const updatedParagraphs = [...paragraphs];
      const temp = updatedParagraphs[index];
      updatedParagraphs[index] = updatedParagraphs[index + 1];
      updatedParagraphs[index + 1] = temp;

      onChange(updatedParagraphs);
    },
    [paragraphs, onChange]
  );

  const getLineTypeLabel = (type: ParagraphType) => {
    switch (type) {
      case "lines":
        return "台詞";
      case "lyrics":
        return "歌";
      case "directions":
        return "ト書き";
      default:
        return "不明";
    }
  };

  if (paragraphs.length === 0) {
    return (
      <div className="py-12 text-center text-muted-foreground">
        <div className="mb-4 text-4xl">📝</div>
        <p>PDFを解析すると、編集可能なテキストがここに表示されます</p>
        <p className="mt-2 text-sm">「解析開始」ボタンをクリックしてください</p>
      </div>
    );
  }

  return (
    <div className="overflow-hidden border rounded-lg border-border bg-muted">
      <div className="max-h-[700px] overflow-auto">
        {/* 縦書きグリッド：右から左へ列が並ぶ */}
        <div className="flex flex-row-reverse gap-3 p-6 min-w-max">
          {paragraphs.map((paragraph, index) => {
            const has_unreadable_chars = paragraph.content.includes("●");

            return (
              <div
                key={index}
                className={`flex flex-col border-2 rounded-lg p-2 min-h-[400px] min-w-10 shadow-sm ${
                  has_unreadable_chars
                    ? "bg-yellow-500/10 border-yellow-500/30"
                    : "bg-card border-border"
                }`}
              >
                {/* 列番号と移動ボタン */}
                <div className="flex items-center justify-between gap-1 mb-2 rounded text-muted-foreground bg-secondary">
                  {/* 左移動ボタン */}
                  <button
                    onClick={() => handleMoveParagraphLeft(index)}
                    disabled={index === paragraphs.length - 1}
                    className={`w-6 h-6 text-xs rounded transition-colors ${
                      index === paragraphs.length - 1
                        ? "bg-muted text-muted-foreground cursor-not-allowed"
                        : "border border-border text-primary hover:bg-primary hover:text-background"
                    }`}
                    title="左に移動"
                  >
                    ←
                  </button>

                  {/* 列番号 */}
                  <div className="flex-1 py-1 text-sm font-bold text-center ">
                    {index + 1}
                  </div>

                  {/* 右移動ボタン */}
                  <button
                    onClick={() => handleMoveParagraphRight(index)}
                    disabled={index === 0}
                    className={`w-6 h-6 text-xs rounded transition-colors ${
                      index === 0
                        ? "bg-muted text-muted-foreground cursor-not-allowed"
                        : "border border-border text-primary  hover:bg-primary hover:text-background"
                    }`}
                    title="右に移動"
                  >
                    →
                  </button>
                </div>

                {/* 役名（縦書き） */}

                {paragraph.type === "lines" && (
                  <textarea
                    value={paragraph.header || ""}
                    onChange={(e) =>
                      handleUpdateParagraph(index, "header", e.target.value)
                    }
                    className={`w-full h-16 px-2 py-2 text-sm font-medium border-2 rounded-md resize-none bg-primary/10 border-primary/20 focus:border-primary focus:outline-none text-card-foreground ${notoSerifJP.className}`}
                    placeholder="役名"
                    style={{
                      writingMode: "vertical-rl",
                      textOrientation: "upright",
                      lineHeight: "1.4",
                    }}
                  />
                )}

                {/* セリフ（縦書き） */}
                <div className="flex flex-col justify-end flex-1 my-2">
                  {/* 歌詞の場合は山形を表示 */}
                  {paragraph.type === "lyrics" && (
                    <p
                      className={`px-2 py-2 mx-1 text-sm font-medium text-end ${notoSerifJP.className}`}
                    >
                      〽
                    </p>
                  )}
                  <textarea
                    value={
                      paragraph.type === "directions"
                        ? "ト、" + paragraph.content
                        : paragraph.content
                    }
                    onChange={(e) =>
                      handleUpdateParagraph(
                        index,
                        "content",
                        e.target.value.replace("ト、", "")
                      )
                    }
                    className={`w-full text-sm border-2 rounded-md px-2 py-2 resize-none font-medium text-card-foreground ${
                      notoSerifJP.className
                    } ${
                      has_unreadable_chars
                        ? "bg-yellow-500/10 border-yellow-500/30"
                        : "bg-green-500/10 border-green-500/20"
                    } focus:outline-none focus:border-green-500`}
                    placeholder="セリフ"
                    cols={
                      paragraph.type === "lines"
                        ? 49
                        : paragraph.type === "lyrics"
                        ? 42
                        : 38
                    }
                    wrap="hard"
                    style={{
                      writingMode: "vertical-rl",
                      textOrientation: "upright",
                      lineHeight: "1.6",
                      letterSpacing: "0.05em",
                    }}
                  />
                  {has_unreadable_chars && (
                    <div className="absolute -top-1 -right-1 bg-yellow-500 text-white text-xs px-1 py-0.5 rounded-full">
                      ⚠️
                    </div>
                  )}
                </div>

                {/* タイプ選択（ドロップダウン） */}
                <div className="mb-2">
                  <select
                    value={paragraph.type}
                    onChange={(e) =>
                      handleUpdateParagraph(
                        index,
                        "type",
                        e.target.value as ParagraphType
                      )
                    }
                    className="w-full px-1 py-1 text-xs text-center border rounded bg-secondary text-muted-foreground border-border focus:outline-none focus:border-primary"
                  >
                    <option value="lines">台詞</option>
                    <option value="lyrics">歌詞</option>
                    <option value="directions">ト書き</option>
                  </select>
                </div>

                {/* 削除ボタン */}
                <div>
                  <button
                    onClick={() => handleDeleteParagraph(index)}
                    className="w-full py-1 text-xs transition-colors border rounded-md text-destructive hover:text-destructive-foreground hover:bg-destructive border-destructive/30 hover:border-destructive"
                  >
                    削除
                  </button>
                </div>
              </div>
            );
          })}
          <button
            onClick={handleAddParagraph}
            className="px-4 py-2 font-medium border rounded-lg border-border text-primary hover:bg-primary hover:text-background"
          >
            + 段落追加
          </button>
        </div>
      </div>
    </div>
  );
}
